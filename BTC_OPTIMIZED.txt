//@version=6
indicator("BTC Big Move Hunter v2.3 - OPTIMIZED", shorttitle="BTC_SNIPER_V3_OPT", overlay=true, max_boxes_count=50, max_labels_count=50)

// =================== SETTINGS ===================
// Big Move Settings - OPTIMIZED
// MODIFICATION: Added setting for Aggressive Entry Mode
use_aggressive_entry = input.bool(false, "Use Aggressive Entry Mode", group="Big Move Settings", tooltip="If true, enters on the first SuperTrend flip instead of waiting for 3-bar stability. Captures trends earlier but may have more false signals.")
min_confluence = input.int(4, "Minimum Confluence", minval=3, maxval=7, group="Big Move Settings", tooltip="Minimum number of confirming conditions required for a signal.")
trend_strength_required = input.int(20, "Minimum ADX for Strong Trend", minval=15, maxval=40, group="Big Move Settings", tooltip="Minimum ADX value on 1H timeframe to consider the trend strong.")
signal_cooldown = input.int(10, "Signal Cooldown (bars)", minval=5, maxval=50, group="Big Move Settings", tooltip="Minimum number of bars to wait before a new signal in the same direction can appear.")

// IMPROVED: Volume surge multiplier as input
volume_multiplier = input.float(1.5, "Volume Surge Multiplier", minval=1.2, maxval=2.5, step=0.1, group="Big Move Settings", tooltip="Multiplier for average volume to detect a volume surge.")

// Entry Timeframe Settings
entry_timeframe = input.string("15", "Entry Timeframe RSI", options=["1", "3", "5", "15"], group="Entry Settings", tooltip="Timeframe used for Entry RSI and primary SuperTrend.")

// NEW: Entry confirmation settings
use_entry_confirmation = input.bool(true, "Use Entry Confirmation", group="Entry Settings", tooltip="If true, an additional confirmation condition must be met for the signal.")
confirm_type = input.string("Structure Break", "Confirmation Type", options=["Structure Break", "RSI Cross", "Engulfing"], group="Entry Settings", tooltip="Type of confirmation: Market structure break, RSI crossing 50, or Engulfing candle pattern.")

// Visual Settings
show_signals = input.bool(true, "Show Entry Signals", group="Display", tooltip="Show/Hide Buy/Sell signal arrows on the chart.")
show_table = input.bool(false, "Show Information Table", group="Display", tooltip="Show/Hide the debug information table.")
show_structure = input.bool(true, "Show Market Structure", group="Display", tooltip="Show/Hide SuperTrend line, EMA 200, and background colors.")
show_signal_strength = input.bool(true, "Show Signal Strength", group="Display", tooltip="Show/Hide signal strength text with signals.")
show_tsl_details = input.bool(true, "Show TSL Details on Chart", group="Display", tooltip="Show/Hide ATR TSL line, ATR TSL exit markers, and SuperTrend TSL exit markers/lines.")
show_chop_filter = input.bool(true, "Show Chop Filter Overlay", group="Display", tooltip="Colors the background gray when trades are blocked by the chop filter.")

// NEW: Chop Filter Settings
use_chop_filter = input.bool(true, "Use Chop Filter", group="Chop Filter", tooltip="Enable the chop filter to avoid trading in sideways markets.")
chop_score_threshold = input.int(2, "Chop Score Threshold", group="Chop Filter", minval=1, maxval=4, tooltip="Block trades if the number of active chop conditions meets or exceeds this value. Default of 2 is more aggressive.")
chop_filter_threshold = input.float(61.8, "Choppiness Index Threshold", group="Chop Filter", minval=0, maxval=100, step=0.1)
adx_chop_threshold = input.int(15, "ADX Chop Threshold", group="Chop Filter", minval=10, maxval=25)
flat_ma_threshold = input.float(0.0005, "Flat MA Threshold (%)", group="Chop Filter", minval=0.0001, maxval=0.01, step=0.0001)
bbw_atr_ratio_threshold = input.float(1.0, "BBW/ATR Ratio Threshold", group="Chop Filter", minval=0.1, maxval=3.0, step=0.1)

// NEW: Trailing stop settings
use_trailing_stop = input.bool(true, "Use Trailing Stop", group="Risk Management", tooltip="Enable ATR-based Trailing Stop Loss.")
trailing_activation = input.float(3.0, "Trailing Activation (R:R)", minval=1.5, maxval=5.0, step=0.5, group="Risk Management", tooltip="R:R ratio (based on initial SL) at which the ATR Trailing Stop activates.")
atr_tsl_multiplier = input.float(2.5, "ATR TSL Multiplier (Standard)", minval=1.0, maxval=5.0, step=0.1, group="Risk Management", tooltip="ATR multiplier for TSL in normal conditions. Higher values make it looser.")
initial_risk_atr_multiplier = input.float(2.5, "Initial Risk ATR Multiplier", minval=1.0, maxval=5.0, step=0.1, group="Risk Management", tooltip="ATR multiplier for calculating the initial conceptual stop distance (risk amount).")
atr_tsl_period = input.int(40, "ATR TSL Period", minval=10, maxval=100, group="Risk Management", tooltip="ATR period for calculating the ATR trailing stop distance. Longer periods are less sensitive.")
tsl_exit_on_close = input.bool(false, "TSL Exit on Candle Close", group="Risk Management", tooltip="If true, ATR TSL requires a candle close beyond the TSL to trigger an exit. If false, exits on wick touch.")
atr_tsl_strong_trend_multiplier = input.float(3.5, "ATR TSL Multiplier (Strong Trend)", minval=1.5, maxval=7.0, step=0.1, group="Risk Management", tooltip="ATR multiplier for TSL when a strong trend is detected. Should be >= Standard Multiplier.")

// --- Enhanced Inputs for Dynamic Filtering and Momentum Prevention ---
stoch_k = input.int(5, "Stochastic K Period", minval=3, maxval=14, group="Momentum Filter")
stoch_d = input.int(3, "Stochastic D Period", minval=2, maxval=5, group="Momentum Filter")

// V1 Scoring System Constants
V1_SCORE_DIFF_THRESHOLD = 1.5 // Minimum score difference for a V1 style signal

// NEW: Higher-Timeframe Filter Settings
filter_type = input.string("None", "Higher Timeframe Filter", options=["None", "DailyEMA", "WeeklyEMA", "Donchian"], group="Confluence Extras", tooltip="Apply an additional filter based on higher timeframe analysis.")
daily_ema_len = input.int(21, "Daily EMA Length", minval=5, group="Confluence Extras", tooltip="Length for Daily EMA filter.")
weekly_ema_len = input.int(10, "Weekly EMA Length", minval=3, group="Confluence Extras", tooltip="Length for Weekly EMA filter.")
donchian_len = input.int(20, "Donchian Channel Length", minval=5, group="Confluence Extras", tooltip="Length for Donchian Channel breakout filter.")

// =================== PERFORMANCE OPTIMIZATION - CACHED SECURITY CALLS ===================
// OPTIMIZATION: Cache all request.security() calls to avoid redundant calculations
var cached_daily_ema = 0.0
var cached_weekly_ema = 0.0
var cached_daily_donchian_upper = 0.0
var cached_daily_donchian_lower = 0.0
var cached_ema_50_1h = 0.0
var cached_ema_200_1h = 0.0
var cached_ema_200_4h = 0.0
var cached_rsi_entry = 0.0
var cached_rsi_15m = 0.0
var cached_rsi_1h = 0.0
var cached_macd_line_1h = 0.0
var cached_signal_line_1h = 0.0
var cached_hist_1h = 0.0
var cached_adx_1h = 0.0
var cached_st_entry = 0.0
var cached_direction_entry = 0
var cached_st_15m = 0.0
var cached_direction_15m = 0
var cached_st_1h = 0.0
var cached_direction_1h = 0

// Update cache once per bar
if barstate.isnew or bar_index == 0
    cached_daily_ema := request.security(syminfo.tickerid, "D", ta.ema(close, daily_ema_len), lookahead=barmerge.lookahead_off)
    cached_weekly_ema := request.security(syminfo.tickerid, "W", ta.ema(close, weekly_ema_len), lookahead=barmerge.lookahead_off)
    cached_daily_donchian_upper := request.security(syminfo.tickerid, "D", ta.highest(high, donchian_len), lookahead=barmerge.lookahead_off)
    cached_daily_donchian_lower := request.security(syminfo.tickerid, "D", ta.lowest(low, donchian_len), lookahead=barmerge.lookahead_off)
    cached_ema_50_1h := request.security(syminfo.tickerid, "60", ta.ema(close, 50), lookahead=barmerge.lookahead_off)
    cached_ema_200_1h := request.security(syminfo.tickerid, "60", ta.ema(close, 200), lookahead=barmerge.lookahead_off)
    cached_ema_200_4h := request.security(syminfo.tickerid, "240", ta.ema(close, 200), lookahead=barmerge.lookahead_off)
    cached_rsi_entry := request.security(syminfo.tickerid, entry_timeframe, ta.rsi(close, 14), lookahead=barmerge.lookahead_off)
    cached_rsi_15m := request.security(syminfo.tickerid, "15", ta.rsi(close, 14), lookahead=barmerge.lookahead_off)
    cached_rsi_1h := request.security(syminfo.tickerid, "60", ta.rsi(close, 14), lookahead=barmerge.lookahead_off)
    [macd_temp, signal_temp, hist_temp] = request.security(syminfo.tickerid, "60", ta.macd(close, 12, 26, 9), lookahead=barmerge.lookahead_off)
    cached_macd_line_1h := macd_temp
    cached_signal_line_1h := signal_temp
    cached_hist_1h := hist_temp
    cached_adx_1h := request.security(syminfo.tickerid, "60", adx_calc(14), lookahead=barmerge.lookahead_off)
    [st_temp_entry, dir_temp_entry] = request.security(syminfo.tickerid, entry_timeframe, ta.supertrend(3, 10), lookahead=barmerge.lookahead_off)
    cached_st_entry := st_temp_entry
    cached_direction_entry := dir_temp_entry
    [st_temp_15m, dir_temp_15m] = request.security(syminfo.tickerid, "15", ta.supertrend(3, 10), lookahead=barmerge.lookahead_off)
    cached_st_15m := st_temp_15m
    cached_direction_15m := dir_temp_15m
    [st_temp_1h, dir_temp_1h] = request.security(syminfo.tickerid, "60", ta.supertrend(3, 10), lookahead=barmerge.lookahead_off)
    cached_st_1h := st_temp_1h
    cached_direction_1h := dir_temp_1h

// Use cached values instead of direct request.security() calls
daily_ema_val = cached_daily_ema
weekly_ema_val = cached_weekly_ema
daily_donchian_upper = cached_daily_donchian_upper
daily_donchian_lower = cached_daily_donchian_lower
ema_50_1h = cached_ema_50_1h
ema_200_1h = cached_ema_200_1h
ema_200_4h = cached_ema_200_4h
rsi_entry = cached_rsi_entry
rsi_15m = cached_rsi_15m
rsi_1h = cached_rsi_1h
macd_line_1h = cached_macd_line_1h
signal_line_1h = cached_signal_line_1h
hist_1h = cached_hist_1h
adx_1h = cached_adx_1h
st_entry = cached_st_entry
direction_entry = cached_direction_entry
st_15m = cached_st_15m
direction_15m = cached_direction_15m
st_1h = cached_st_1h
direction_1h = cached_direction_1h

daily_bullish = close > daily_ema_val
weekly_bullish = close > weekly_ema_val
donch_break_long = close > daily_donchian_upper[1]
donch_break_short = close < daily_donchian_lower[1]

// =================== ADX CALCULATION FUNCTION ===================
adx_calc(len) =>
    up = ta.change(high)
    down = -ta.change(low)
    plusDM_raw = up > down and up > 0 ? up : 0
    minusDM_raw = down > up and down > 0 ? down : 0
    plusDM = na(up) ? na : plusDM_raw
    minusDM = na(down) ? na : minusDM_raw
    truerange = ta.rma(ta.tr, len)
    smoothed_plusDM = ta.rma(plusDM, len)
    smoothed_minusDM = ta.rma(minusDM, len)
    plus = fixnan(100 * smoothed_plusDM / truerange)
    minus = fixnan(100 * smoothed_minusDM / truerange)
    sum = plus + minus
    dx = sum == 0 ? 0 : math.abs(plus - minus) / sum
    adx = 100 * ta.rma(dx, len)
    adx

// =================== TREND ANALYSIS ===================
trend_1h = close > ema_200_1h ? 1 : -1
trend_4h = close > ema_200_4h ? 1 : -1

trend_alignment = math.abs(trend_1h + trend_4h)
major_trend_direction = (trend_1h + trend_4h) > 0 ? 1 : -1

strong_trend_condition = trend_alignment >= 2

// =================== MOMENTUM INDICATORS ===================
rsi_cross_up = ta.crossover(rsi_entry, 50)
rsi_cross_down = ta.crossunder(rsi_entry, 50)

k = ta.stoch(close, high, low, stoch_k)
d = ta.sma(k, stoch_d)
momentum_bullish = k > 20
momentum_bearish = k < 80

st_bullish = direction_entry == -1
st_bearish = direction_entry == 1

st_15m_bullish = direction_15m == -1
st_1h_bullish = direction_1h == -1

st_stable_bullish = st_bullish and st_bullish[1] and st_bullish[2]
st_stable_bearish = st_bearish and st_bearish[1] and st_bearish[2]

st_alignment_bull = (st_bullish ? 1 : 0) + (st_15m_bullish ? 1 : 0) + (st_1h_bullish ? 1 : 0)
st_alignment_bear = (st_bearish ? 1 : 0) + (not st_15m_bullish ? 1 : 0) + (not st_1h_bullish ? 1 : 0)

// =================== VOLUME ANALYSIS ===================
volume_ma = ta.sma(volume, 20)
volume_surge = volume > volume_ma * volume_multiplier

// =================== PERFORMANCE OPTIMIZATION ===================
should_calculate = bar_index > 50

// =================== OPTIMIZED HELPER FUNCTIONS ===================
// OPTIMIZATION: Helper function for chop score calculation
calculate_chop_score() =>
    chopIdx = math.log10(math.sum(ta.tr, 14) / (ta.highest(high, 14) - ta.lowest(low, 14))) * 100
    flatMA_val = ema_200_1h > 0 ? math.abs(ta.change(ema_200_1h, 10) / ema_200_1h) : 0
    [bb_middle, bb_upper, bb_lower] = ta.bb(close, 20, 2)
    bb_width = bb_upper - bb_lower
    atr_short = ta.atr(10)
    bbw_atr_ratio = atr_short > 0 ? (bb_width / atr_short) : 0

    is_choppy_idx = chopIdx > chop_filter_threshold
    is_adx_weak = adx_1h < adx_chop_threshold
    is_ma_flat = flatMA_val < flat_ma_threshold
    is_bbw_tight = bbw_atr_ratio < bbw_atr_ratio_threshold

    chop_score = (is_choppy_idx ? 1 : 0) + (is_adx_weak ? 1 : 0) + (is_ma_flat ? 1 : 0) + (is_bbw_tight ? 1 : 0)
    chop_score

// OPTIMIZATION: Helper function for trend condition checks
check_trend_condition(direction, trend_dir, score_increment, confluence_increment) =>
    condition_met = direction == trend_dir
    score_add = condition_met ? score_increment : 0.0
    confluence_add = condition_met ? confluence_increment : 0
    [condition_met, score_add, confluence_add]

// OPTIMIZATION: Helper function for RSI condition checks
check_rsi_condition(rsi_val, threshold, comparison_type, rsi_15m_val, rsi_1h_val, rsi_15m_limit, rsi_1h_limit) =>
    rsi_condition = comparison_type == "buy" ?
                   (rsi_val < threshold and rsi_15m_val < rsi_15m_limit and rsi_1h_val < rsi_1h_limit) :
                   (rsi_val > threshold and rsi_15m_val > rsi_15m_limit and rsi_1h_val > rsi_1h_limit)

    condition_met = not na(rsi_val) and not na(rsi_15m_val) and not na(rsi_1h_val) and rsi_condition
    score_add = condition_met ? 2.0 : 0.0
    confluence_add = condition_met ? 1 : 0
    [condition_met, score_add, confluence_add]

// OPTIMIZATION: Helper function for SuperTrend alignment checks
check_supertrend_alignment(stable_condition, alignment_count, aggressive_mode) =>
    if aggressive_mode
        // Use single SuperTrend condition for aggressive mode
        condition_met = alignment_count >= 2
        score_add = condition_met ? 1.0 : 0.0
    else
        // Use stable SuperTrend condition for conservative mode
        condition_met = stable_condition and alignment_count >= 2
        score_add = condition_met ? 2.0 : 0.0

    confluence_add = condition_met ? 1 : 0
    [condition_met, score_add, confluence_add]

// OPTIMIZATION: Helper function for shared conditions
calculate_shared_conditions() =>
    adx_strong = not na(adx_1h) and adx_1h >= trend_strength_required
    vol_surge = volume_surge
    [adx_strong, vol_surge]

// =================== V1 PIVOT-BASED MARKET STRUCTURE ===================
v1_pivot_high_val = ta.pivothigh(high, 15, 15)
v1_pivot_low_val = ta.pivotlow(low, 15, 15)

var v1_recent_highs = array.new<float>()
var v1_recent_lows = array.new<float>()

if not na(v1_pivot_high_val)
    array.push(v1_recent_highs, v1_pivot_high_val)
    if array.size(v1_recent_highs) > 3
        array.shift(v1_recent_highs)

if not na(v1_pivot_low_val)
    array.push(v1_recent_lows, v1_pivot_low_val)
    if array.size(v1_recent_lows) > 3
        array.shift(v1_recent_lows)

v1_market_structure = "RANGING"
v1_structure_strength_adjust = 0

if array.size(v1_recent_highs) >= 2 and array.size(v1_recent_lows) >= 2
    v1_hh_count = 0
    v1_hl_count = 0
    v1_lh_count = 0
    v1_ll_count = 0

    if array.size(v1_recent_highs) > 1
        for i = 1 to array.size(v1_recent_highs) - 1
            if array.get(v1_recent_highs, i) > array.get(v1_recent_highs, i - 1)
                v1_hh_count += 1
            else
                v1_lh_count += 1

    if array.size(v1_recent_lows) > 1
        for i = 1 to array.size(v1_recent_lows) - 1
            if array.get(v1_recent_lows, i) > array.get(v1_recent_lows, i - 1)
                v1_hl_count += 1
            else
                v1_ll_count += 1

    v1_bullish_signals = v1_hh_count + v1_hl_count
    v1_bearish_signals = v1_lh_count + v1_ll_count

    if v1_bullish_signals > v1_bearish_signals + 1
        v1_market_structure := "BULLISH"
        v1_structure_strength_adjust := 5
    else if v1_bearish_signals > v1_bullish_signals + 1
        v1_market_structure := "BEARISH"
        v1_structure_strength_adjust := -5

// =================== V1 VOLATILITY REGIME & DYNAMIC RSI ===================
atr_short = ta.atr(10)
atr_long = ta.atr(40)
volatility_ratio = atr_short / atr_long

volatility_regime = switch
    volatility_ratio < 0.8 => "LOW"
    volatility_ratio < 1.2 => "MEDIUM"
    volatility_ratio < 1.5 => "HIGH"
    => "EXTREME"

min_confluence_dynamic = switch volatility_regime
    "LOW" => min_confluence + 1
    "MEDIUM" => min_confluence
    "HIGH" => min_confluence - 1
    "EXTREME" => min_confluence - 1

v1_volatility_regime = "MEDIUM"
v1_rsi_buy_threshold = 25
v1_rsi_sell_threshold = 75
v1_vol_adjustment = 0
float v1_vol_percentile = na

if should_calculate
    v1_returns = math.log(close / close[1])
    v1_long_vol = ta.stdev(v1_returns, 200)
    v1_vol_percentile := ta.percentrank(v1_long_vol, 200)

    v1_volatility_regime := switch
        v1_vol_percentile < 25 => "LOW"
        v1_vol_percentile < 50 => "MEDIUM"
        v1_vol_percentile < 80 => "HIGH"
        => "EXTREME"

    v1_base_buy_rsi = 30
    v1_base_sell_rsi = 70

    v1_vol_adjustment := switch v1_volatility_regime
        "LOW" => 5
        "HIGH" => -5
        "EXTREME" => -10
        => 0

    v1_rsi_buy_threshold := math.max(15, math.min(45, v1_base_buy_rsi + v1_vol_adjustment + v1_structure_strength_adjust))
    v1_rsi_sell_threshold := math.min(85, math.max(55, v1_base_sell_rsi + v1_vol_adjustment + v1_structure_strength_adjust))

// =================== CHOP / SIDEWAYS MARKET FILTERS ===================
chop_score = calculate_chop_score()
trade_block = use_chop_filter and (chop_score >= chop_score_threshold)

// =================== SIGNAL LOGIC ===================
var string last_signal = ""
var int bars_since_signal = 0

bars_since_signal += 1

// =================== OPTIMIZED BUY SIGNAL CONDITIONS ===================
v1_buy_score = 0.0
v1_actual_buy_confluence_count = 0
var bool condition_st_stable_bullish_aligned = false
var bool condition_st_bullish_aligned = false
var bool condition_st_stable_bearish_aligned = false
var bool condition_st_bearish_aligned = false

if should_calculate
    v1_buy_score := 0.0
    v1_actual_buy_confluence_count := 0

    // OPTIMIZATION: Use helper function for shared conditions
    [shared_condition_adx_strong, shared_condition_volume_surge] = calculate_shared_conditions()

    // OPTIMIZATION: Use helper function for trend checks
    [trend_cond_met, trend_score, trend_conf] = check_trend_condition(major_trend_direction, 1, 1.5, 1)
    v1_buy_score += trend_score
    v1_actual_buy_confluence_count += trend_conf

    // OPTIMIZATION: Use helper function for RSI checks
    [rsi_cond_met, rsi_score, rsi_conf] = check_rsi_condition(rsi_entry, v1_rsi_buy_threshold, "buy", rsi_15m, rsi_1h, 85, 80)
    v1_buy_score += rsi_score
    v1_actual_buy_confluence_count += rsi_conf

    // MACD condition
    condition_macd_bullish = not na(macd_line_1h) and not na(signal_line_1h) and macd_line_1h > signal_line_1h
    if condition_macd_bullish
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1

    // OPTIMIZATION: Use helper function for SuperTrend alignment
    [st_stable_cond, st_stable_score, st_stable_conf] = check_supertrend_alignment(st_stable_bullish, st_alignment_bull, false)
    [st_regular_cond, st_regular_score, st_regular_conf] = check_supertrend_alignment(st_bullish, st_alignment_bull, true)

    condition_st_stable_bullish_aligned := st_stable_cond
    condition_st_bullish_aligned := st_regular_cond

    if condition_st_stable_bullish_aligned
        v1_buy_score += st_stable_score
        v1_actual_buy_confluence_count += st_stable_conf
    else if condition_st_bullish_aligned
        v1_buy_score += st_regular_score
        v1_actual_buy_confluence_count += st_regular_conf

    if shared_condition_adx_strong
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1

    if shared_condition_volume_surge
        v1_buy_score += 0.5
        v1_actual_buy_confluence_count += 1

    condition_price_action_bullish = close > open and close > high[1]
    if condition_price_action_bullish
        v1_buy_score += 0.5
        v1_actual_buy_confluence_count += 1

    condition_v1_structure_bullish = v1_market_structure == "BULLISH"
    if condition_v1_structure_bullish
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1

// =================== OPTIMIZED SELL SIGNAL CONDITIONS ===================
v1_sell_score = 0.0
v1_actual_sell_confluence_count = 0

if should_calculate
    v1_sell_score := 0.0
    v1_actual_sell_confluence_count := 0

    // OPTIMIZATION: Use helper function for shared conditions (already calculated above)
    [shared_condition_adx_strong, shared_condition_volume_surge] = calculate_shared_conditions()

    // OPTIMIZATION: Use helper function for trend checks
    [trend_cond_met, trend_score, trend_conf] = check_trend_condition(major_trend_direction, -1, 1.5, 1)
    v1_sell_score += trend_score
    v1_actual_sell_confluence_count += trend_conf

    // OPTIMIZATION: Use helper function for RSI checks
    [rsi_cond_met, rsi_score, rsi_conf] = check_rsi_condition(rsi_entry, v1_rsi_sell_threshold, "sell", rsi_15m, rsi_1h, 15, 20)
    v1_sell_score += rsi_score
    v1_actual_sell_confluence_count += rsi_conf

    // MACD condition
    condition_macd_bearish = not na(macd_line_1h) and not na(signal_line_1h) and macd_line_1h < signal_line_1h
    if condition_macd_bearish
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1

    // OPTIMIZATION: Use helper function for SuperTrend alignment
    [st_stable_cond, st_stable_score, st_stable_conf] = check_supertrend_alignment(st_stable_bearish, st_alignment_bear, false)
    [st_regular_cond, st_regular_score, st_regular_conf] = check_supertrend_alignment(st_bearish, st_alignment_bear, true)

    condition_st_stable_bearish_aligned := st_stable_cond
    condition_st_bearish_aligned := st_regular_cond

    if condition_st_stable_bearish_aligned
        v1_sell_score += st_stable_score
        v1_actual_sell_confluence_count += st_stable_conf
    else if condition_st_bearish_aligned
        v1_sell_score += st_regular_score
        v1_actual_sell_confluence_count += st_regular_conf

    if shared_condition_adx_strong
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1

    if shared_condition_volume_surge
        v1_sell_score += 0.5
        v1_actual_sell_confluence_count += 1

    condition_price_action_bearish = close < open and close < low[1]
    if condition_price_action_bearish
        v1_sell_score += 0.5
        v1_actual_sell_confluence_count += 1

    condition_v1_structure_bearish = v1_market_structure == "BEARISH"
    if condition_v1_structure_bearish
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1
